import csv
import os
from django.http import HttpResponse
import datetime
from datetime import timedelta
import pytz
import json

import logging

import graphviz

class GraphVizGeneratorController:
    CHAINS_DEPENDENCIES_FILE = "/tmp/poi181.j.basetwsd_mdmpa.json"

    def __init__(self, component):
        self.component = component.upper()

    @classmethod
    def generate_graph_for_component(cls, request=None, app=None, view=None, pk=None):
        generated_file = cls(pk).generate_graph()

        file = open(generated_file, "rb")
        return HttpResponse(file.read(), content_type="image/jpeg")

    def load_file_from_s3(self, output_filename=CHAINS_DEPENDENCIES_FILE):
        import boto3

        s3_client = boto3.client(
            "s3",
            aws_access_key_id=os.getenv("S3_CLIENT"),
            aws_secret_access_key=os.getenv("S3_SECRET"),
            use_ssl=False,
            endpoint_url=os.getenv("S3_URL"),
        )

        s3_client.download_file(
            Bucket=os.getenv("S3_BUCKET"),
            Key=f"poi181.j.basetwsd_mdmpa.json",
            Filename=output_filename,
        )

        return output_filename

    def load(self, file):
        try:
            with open(file, "r") as f:
                data = json.load(f)

            chains = {}

            for jst_obj in data:
                # Récupérer les attributs nécessaires de l'objet Jst
                chaine = jst_obj.get("chaine")
                owner = jst_obj.get("owner")
                workstation = jst_obj.get("workstation")
                description = jst_obj.get("description")
                follows = jst_obj.get("follows")

                # Vérifier si la chaîne existe déjà dans le dictionnaire chains
                if owner is not None and chaine not in chains:
                    chains[chaine] = {
                        "chain": chaine,
                        "component": owner,
                        "workstation": workstation,
                        "description": description,
                        "follows": follows,
                    }

            return chains
        except Exception as e:
            logging.error(f"Failed to load file : {e}")

    def flatten_chains(self, chains):
        uniq_chain = []

        for chain, data in chains.items():
            if chain not in uniq_chain:
                uniq_chain.append(chain)

        return uniq_chain

    def get_color_for_chain(self, chain):
        """
        Return border color for a specific chain
        """
        # Use darker colors for borders to improve contrast
        colors = {
            "D": "orange",
            "J": "blue",
            "V": "darkblue",
            "P": "darkblue",
            "G": "darkblue",
            "E": "orange",
            "K": "darkgreen",
            "W": "darkgreen",
            "Q": "darkgreen",
            "H": "darkgreen",
            "M": "black",
        }
        return colors.get(chain[-1], "black")

    @classmethod
    def get_fillcolor_for_chain(cls, chain):
        """
        Return color for a specific chain
        :param chain:
        :return:
        """
        colors = {
            "D": "lightyellow1",
            "J": "lightskyblue3",
            "V": "lightskyblue4",
            "P": "lightskyblue4",
            "G": "lightskyblue4",
            "E": "lightyellow1",
            "K": "green3",
            "W": "green4",
            "Q": "green4",
            "H": "green4",
            "M": "grey",
        }

        return colors.get(chain[-1], "white")

    def _configure_graph_layout(self, diagram):
        """
        Configure graph layout attributes for better organization and readability
        """
        # Graph-level attributes for better layout
        diagram.attr(
            # Layout algorithm - 'dot' for hierarchical, 'neato' for spring model
            engine='dot',  # Change to 'neato', 'fdp', or 'circo' for different layouts

            # Spacing and organization
            ranksep='1.5',      # Vertical spacing between ranks
            nodesep='1.0',      # Horizontal spacing between nodes
            mindist='1.0',      # Minimum distance between nodes (for neato/fdp)

            # Graph size and DPI
            size='20,20',       # Maximum graph size in inches
            dpi='150',          # Resolution for better quality

            # Layout direction and organization
            rankdir='TB',       # Top to Bottom (use 'LR' for Left to Right)

            # Edge routing
            splines='ortho',    # Orthogonal edges (use 'curved' or 'polyline' for alternatives)

            # Background and margins
            bgcolor='white',
            margin='0.5',

            # Overlap prevention
            overlap='false',
            sep='+20',          # Additional separation
        )

        # Node defaults for consistency
        diagram.attr('node',
            fontname='Arial',
            fontsize='12',
            width='2.0',        # Minimum node width
            height='1.0',       # Minimum node height
            fixedsize='false',  # Allow nodes to grow with content
            penwidth='2',       # Border thickness
        )

        # Edge defaults for better visibility
        diagram.attr('edge',
            fontname='Arial',
            fontsize='10',
            penwidth='2',       # Edge thickness
            arrowsize='1.0',    # Arrow size
            minlen='2',         # Minimum edge length
        )

    def _group_chains_by_type(self, chains):
        """
        Group chains by their type (last character) for better organization
        """
        groups = {}
        for key, chain in chains.items():
            chain_type = key[-1] if key else 'unknown'
            if chain_type not in groups:
                groups[chain_type] = []
            groups[chain_type].append((key, chain))
        return groups

    def _add_nodes_with_layout(self, diagram, grouped_chains):
        """
        Add nodes with improved layout and grouping
        """
        # Sort groups for consistent ordering
        for group_type in sorted(grouped_chains.keys()):
            chains_in_group = grouped_chains[group_type]

            # Create a subgraph for each group to improve organization
            with diagram.subgraph(name=f'cluster_{group_type}') as subgraph:
                subgraph.attr(
                    label=f'Type {group_type}',
                    style='rounded,filled',
                    fillcolor='lightgray',
                    fontsize='14',
                    fontname='Arial Bold',
                    margin='20'
                )

                # Add nodes within the group
                for key, chain in sorted(chains_in_group):
                    # Enhanced node styling
                    subgraph.node(
                        key,
                        label=self._create_enhanced_node_label(key, chain),
                        shape="box",  # Changed from oval for better text display
                        style="filled,rounded",
                        fillcolor=self.get_fillcolor_for_chain(key),
                        color=self.get_color_for_chain(key),
                        fontname='Arial',
                        fontsize='11',
                        margin='0.3,0.2'
                    )

    def _create_enhanced_node_label(self, key, chain):
        """
        Create an enhanced label for nodes with better formatting
        """
        description = chain.get('description', '')
        workstation = chain.get('workstation', '')

        # Truncate long descriptions for better display
        if len(description) > 40:
            description = description[:37] + "..."

        # Create multi-line label with HTML-like formatting
        label_parts = [f"<B>{key}</B>"]

        if description:
            label_parts.append(f"<I>{description}</I>")

        if workstation:
            label_parts.append(f"<FONT POINT-SIZE='9'>{workstation}</FONT>")

        return f"<{chr(10).join(label_parts)}>"

    def file_exists_since_mins(self, filename):
        try:
            return (
                datetime.datetime.now()
                - datetime.datetime.fromtimestamp(os.path.getctime(filename))
            ) / timedelta(minutes=1)
        except:
            return None

    def _add_edges_with_improved_logic(self, diagram, chains):
        """
        Add edges with improved logic to reduce complexity and overlaps
        """
        added_edges = set()

        # Build a proper dependency graph
        dependencies = {}
        for key, chain in chains.items():
            dependencies[key] = set(chain.get('follows', []))

        # Add direct dependencies only (avoid redundant transitive edges)
        for source_key, source_chain in chains.items():
            for dep in source_chain.get('follows', []):
                # Check if this dependency exists in our filtered chains
                if dep in chains:
                    edge_key = (dep, source_key)
                    if edge_key not in added_edges:
                        added_edges.add(edge_key)

                        # Determine edge style based on relationship strength
                        edge_style = self._get_edge_style(dep, source_key, dependencies)

                        diagram.edge(
                            dep,
                            source_key,
                            color=edge_style['color'],
                            style=edge_style['style'],
                            penwidth=edge_style['penwidth'],
                            arrowhead=edge_style['arrowhead'],
                            label=edge_style.get('label', '')
                        )

    def _get_edge_style(self, source, target, dependencies):
        """
        Determine edge styling based on relationship characteristics
        """
        # Count mutual dependencies for styling
        mutual_deps = len(dependencies.get(source, set()) & dependencies.get(target, set()))

        if mutual_deps > 2:
            # Strong relationship - thick, colored edge
            return {
                'color': 'red',
                'style': 'solid',
                'penwidth': '3',
                'arrowhead': 'normal'
            }
        elif mutual_deps > 0:
            # Medium relationship - medium edge
            return {
                'color': 'blue',
                'style': 'solid',
                'penwidth': '2',
                'arrowhead': 'normal'
            }
        else:
            # Simple relationship - standard edge
            return {
                'color': 'black',
                'style': 'solid',
                'penwidth': '1',
                'arrowhead': 'normal'
            }

    def _get_optimal_layout_engine(self, num_nodes, num_edges):
        """
        Suggest optimal layout engine based on graph characteristics
        """
        edge_density = num_edges / max(num_nodes, 1)

        if num_nodes <= 10:
            return 'dot'  # Hierarchical for small graphs
        elif edge_density > 2:
            return 'fdp'  # Force-directed for dense graphs
        elif num_nodes > 50:
            return 'neato'  # Spring model for large graphs
        else:
            return 'dot'  # Default hierarchical

    def generate_graph_with_layout(self, layout_type="auto", output_dir="/tmp", file_format="png"):
        """
        Generate graph with specific layout type for different visualization needs

        Layout types:
        - 'auto': Automatically choose best layout
        - 'hierarchical': Top-down hierarchy (dot)
        - 'circular': Circular layout (circo)
        - 'spring': Spring model layout (neato)
        - 'force': Force-directed layout (fdp)
        - 'compact': Compact layout for large graphs (sfdp)
        """
        layout_engines = {
            'hierarchical': 'dot',
            'circular': 'circo',
            'spring': 'neato',
            'force': 'fdp',
            'compact': 'sfdp'
        }

        if layout_type == "auto":
            return self.generate_graph(output_dir, file_format)
        elif layout_type in layout_engines:
            # Use specific layout engine
            return self._generate_graph_with_engine(layout_engines[layout_type], output_dir, file_format)
        else:
            raise ValueError(f"Unknown layout type: {layout_type}")

    def _generate_graph_with_engine(self, engine, output_dir="/tmp", file_format="png"):
        """
        Generate graph with a specific engine
        """
        try:
            final_filename = f"{output_dir}/chains-{self.component.lower()}.gv.{file_format}"

            # Check if chains file is existing and recent.
            if (self.file_exists_since_mins(self.CHAINS_DEPENDENCIES_FILE) or 10000000) < (60 * 4):
                file_path = self.CHAINS_DEPENDENCIES_FILE
            else:
                print("Download file ...")
                file_path = self.load_file_from_s3()

            try:
                chains = self.load(file_path)
                filtered_chains = dict(filter(lambda pair: pair[1]["component"].lower() == self.component.lower(), chains.items()))
            except Exception as load_error:
                logging.error(f"An error occurred while loading and filtering chains: {load_error}")
                return None

            # Diagram generation with specific engine
            file_creation_date = datetime.datetime.now()
            local_timezone = pytz.timezone('UTC')
            file_creation_date_local = local_timezone.localize(file_creation_date)
            france_timezone = pytz.timezone('Europe/Paris')
            file_creation_date_fr = file_creation_date_local.astimezone(france_timezone)
            file_creation_date_label = file_creation_date_fr.strftime("%d/%m/%Y à %H:%M:%S")

            try:
                output_diagram = graphviz.Digraph(
                    f'chains-{self.component.lower()}',
                    format=file_format,
                    engine=engine
                )

                # Configure graph layout and styling for better organization
                self._configure_graph_layout(output_diagram)

                output_diagram.attr(label=f"<<FONT POINT-SIZE='40'>Date d'actualisation: {file_creation_date_label}</FONT>>", labelloc="tl", labeljust="left")

                # Group chains by type for better organization
                grouped_chains = self._group_chains_by_type(filtered_chains)

                # Add nodes with improved styling and positioning
                self._add_nodes_with_layout(output_diagram, grouped_chains)

                # Add edges with improved logic and styling
                self._add_edges_with_improved_logic(output_diagram, filtered_chains)

                output_diagram.render(directory=output_dir).replace('\\', '/')

            except Exception as diagram_error:
                logging.error(f"An error occurred while generating the diagram: {diagram_error}")
                return None

            return final_filename

        except Exception as e:
            logging.error(f"An unexpected error occurred: {e}")
            return None

    def get_layout_recommendations(self, chains):
        """
        Provide layout recommendations based on graph characteristics
        """
        num_nodes = len(chains)
        num_edges = sum(len(chain.get('follows', [])) for chain in chains.values())
        edge_density = num_edges / max(num_nodes, 1)

        recommendations = {
            'graph_stats': {
                'nodes': num_nodes,
                'edges': num_edges,
                'density': round(edge_density, 2)
            },
            'recommended_layouts': []
        }

        if num_nodes <= 10:
            recommendations['recommended_layouts'].append({
                'layout': 'hierarchical',
                'reason': 'Small graph - hierarchical layout shows clear dependencies'
            })

        if edge_density > 2:
            recommendations['recommended_layouts'].append({
                'layout': 'force',
                'reason': 'Dense connections - force-directed layout reduces overlaps'
            })

        if num_nodes > 50:
            recommendations['recommended_layouts'].append({
                'layout': 'compact',
                'reason': 'Large graph - compact layout improves readability'
            })

        if 10 < num_nodes < 30:
            recommendations['recommended_layouts'].append({
                'layout': 'circular',
                'reason': 'Medium-sized graph - circular layout shows relationships well'
            })

        return recommendations

    @classmethod
    def generate_comparison_layouts(cls, component, output_dir="/tmp"):
        """
        Generate graphs with different layouts for comparison
        Returns a dictionary with layout names and their file paths
        """
        controller = cls(component)
        layouts = ['hierarchical', 'circular', 'spring', 'force']
        results = {}

        for layout in layouts:
            try:
                filename = controller.generate_graph_with_layout(
                    layout_type=layout,
                    output_dir=output_dir,
                    file_format="png"
                )
                if filename:
                    # Rename file to include layout type
                    base_name = filename.replace('.gv.png', '')
                    new_filename = f"{base_name}-{layout}.gv.png"
                    os.rename(filename, new_filename)
                    results[layout] = new_filename
            except Exception as e:
                logging.error(f"Failed to generate {layout} layout: {e}")

        return results

    def generate_graph(self, output_dir="/tmp", file_format="png"):
        try: 
            final_filename = f"{output_dir}/chains-{self.component.lower()}.gv.{file_format}"

            # We check if the graph for the component already exists
            if (self.file_exists_since_mins(final_filename) or 10000000) < (60 * 4):
                return final_filename

            # Check if chains file is existing and recent.
            if (self.file_exists_since_mins(self.CHAINS_DEPENDENCIES_FILE) or 10000000) < (60 * 4):
                file_path = self.CHAINS_DEPENDENCIES_FILE
            else:
                print("Download file ...")
                file_path = self.load_file_from_s3()

            try: 
                chains = self.load(file_path)

                filtered_chains = dict(filter(lambda pair: pair[1]["component"].lower() == self.component.lower(), chains.items()))
            except Exception as load_error:
                # Log the error and return None or raise a specific exception
                logging.error(f"An error occurred while loading and filtering chains: {load_error}")
                return None
            
            # Diagram generation
            if (self.file_exists_since_mins(final_filename) or 10000000) < (1 * 4):
                file_creation_date = datetime.datetime.now() - timedelta(minutes=self.file_exists_since_mins(final_filename))
            else:
                file_creation_date = datetime.datetime.now()

            local_timezone = pytz.timezone('UTC')  # ou pytz.timezone('Etc/UTC') pour l'heure UTC

            file_creation_date_local = local_timezone.localize(file_creation_date)
            france_timezone = pytz.timezone('Europe/Paris')
            file_creation_date_fr = file_creation_date_local.astimezone(france_timezone)
            file_creation_date_label = file_creation_date_fr.strftime("%d/%m/%Y à %H:%M:%S")

            try:
                # Determine optimal layout engine based on graph size
                num_nodes = len(filtered_chains)
                num_edges = sum(len(chain.get('follows', [])) for chain in filtered_chains.values())
                optimal_engine = self._get_optimal_layout_engine(num_nodes, num_edges)

                output_diagram = graphviz.Digraph(
                    f'chains-{self.component.lower()}',
                    format=file_format,
                    engine=optimal_engine
                )

                # Configure graph layout and styling for better organization
                self._configure_graph_layout(output_diagram)

                output_diagram.attr(label=f"<<FONT POINT-SIZE='40'>Date d'actualisation: {file_creation_date_label}</FONT>>", labelloc="tl", labeljust="left")

                # Group chains by type for better organization
                grouped_chains = self._group_chains_by_type(filtered_chains)

                # Add nodes with improved styling and positioning
                self._add_nodes_with_layout(output_diagram, grouped_chains)

                # Add edges with improved logic and styling
                self._add_edges_with_improved_logic(output_diagram, filtered_chains)

                output_diagram.render(directory=output_dir).replace('\\', '/')

            except Exception as diagram_error:
                logging.error(f"An error occurred while generating the diagram: {diagram_error}")
                return None
            
            return final_filename
        
        except Exception as e:
            logging.error(f"An unexpected error occurred: {e}")
            return None