# Image de base
FROM python:3.6.13-alpine

# Set proxies
RUN cat /etc/os-release
RUN echo "http://artefact-repo.pole-emploi.intra/artifactory/AlpineLinux-Proxy/alpine/v3.13/main/" > /etc/apk/repositories
RUN echo "http://artefact-repo.pole-emploi.intra/artifactory/AlpineLinux-Proxy/alpine/v3.13/community/" >> /etc/apk/repositories

# Install python requirements
RUN pip config --user set global.index http://artefact-repo.pole-emploi.intra/artifactory/api/pypi/Pypi-Proxy/
RUN pip config --user set global.index-url http://artefact-repo.pole-emploi.intra/artifactory/api/pypi/Pypi-Proxy/simple/
RUN pip config --user set global.trusted-host artefact-repo.pole-emploi.intra
RUN python -m pip install --upgrade pip

# /app/ dossier de l'application
RUN mkdir /app/
WORKDIR /app/

# Installation des dépendances pour PostgresSQL
RUN \
 apk add --no-cache postgresql-libs && \
 apk add --no-cache --virtual .build-deps gcc musl-dev postgresql-dev

# GraphViz
RUN apk add --update --no-cache graphviz ttf-freefont

# Copie du fichier requirements.txt
COPY srcs/api/requirements.txt /app/

# Utilisation de pip pour installer les dépendances
RUN pip install -r requirements.txt

# PE Python librarie
RUN apk add --no-cache --virtual .build-deps cmake build-base openssl openssl-dev zlib-dev libffi-dev libxml2-dev libxslt-dev
RUN apk --no-cache --virtual .locale_build add gcc musl-dev python3-dev libffi-dev openssl-dev cargo git
RUN git -c http.sslVerify=false clone https://git-scm.pole-emploi.intra/osp/pole-emploi-python-librairie.git && cd pole-emploi-python-librairie && python setup.py bdist_wheel && pip install --force-reinstall  --user dist/* && cd .. && rm -r pole-emploi-python-librairie


# Copie des sources
COPY srcs/api/ /app/

# Variables d'environnement
ENV PYTHONUNBUFFERED=1

# Purge = gain de place
RUN apk --purge del .build-deps cmake build-base gcc .locale_build

# SSL Certificates
COPY ./infra/docker/api/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt
ENV REQUESTS_CA_BUNDLE=/etc/ssl/certs/ca-certificates.crt

# Copie de l'entrypoint
COPY infra/docker/api/entrypoint.sh /entrypoint.sh

ENV DJANGO_SETTINGS_MODULE=sdc.settings
ENV PYTHONPATH=/app/

ENTRYPOINT ["sh", "/entrypoint.sh"]
